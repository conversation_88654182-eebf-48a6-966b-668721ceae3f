// Application State
let selectedServices = [];
let customerData = {};

// Check if running in Electron
const isElectron = typeof window !== 'undefined' && window.process && window.process.type;

// Electron specific functionality
if (isElectron) {
    const { ipcRenderer } = require('electron');

    // Enhanced save/load functionality for Electron
    window.electronAPI = {
        saveFile: async (data, filename) => {
            const result = await ipcRenderer.invoke('show-save-dialog', {
                defaultPath: filename,
                filters: [
                    { name: 'JSON súbory', extensions: ['json'] },
                    { name: 'Vš<PERSON>ky súbory', extensions: ['*'] }
                ]
            });

            if (!result.canceled) {
                const writeResult = await ipcRenderer.invoke('write-file', result.filePath, data);
                return writeResult;
            }
            return { success: false, canceled: true };
        },

        openFile: async () => {
            const result = await ipcRenderer.invoke('show-open-dialog', {
                properties: ['openFile'],
                filters: [
                    { name: 'JSON súbory', extensions: ['json'] },
                    { name: '<PERSON>š<PERSON><PERSON> súbory', extensions: ['*'] }
                ]
            });

            if (!result.canceled && result.filePaths.length > 0) {
                const readResult = await ipcRenderer.invoke('read-file', result.filePaths[0]);
                return readResult;
            }
            return { success: false, canceled: true };
        }
    };
}

// DOM Elements (will be initialized after DOM loads)
let serviceCheckboxes;
let customerForm;
let selectedServicesList;
let subtotalElement;
let vatElement;
let totalElement;
let generatePDFButton;

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DOM elements
    serviceCheckboxes = document.querySelectorAll('input[type="checkbox"][data-service]');
    customerForm = document.querySelector('.customer-section');
    selectedServicesList = document.getElementById('selectedServicesList');
    subtotalElement = document.getElementById('subtotal');
    vatElement = document.getElementById('vat');
    totalElement = document.getElementById('total');
    generatePDFButton = document.querySelector('.btn-generate');

    initializeEventListeners();
    updateCalculation();
    initializeNavigation();

    // Check if html2pdf is loaded
    setTimeout(checkHtml2PdfAvailability, 1000);

    // Initialize tab system
    initializeTabSystem();
});

function checkHtml2PdfAvailability() {
    console.log('Checking html2pdf availability...');
    console.log('generatePDFButton:', generatePDFButton);

    if (typeof window.html2pdf === 'undefined') {
        console.warn('html2pdf is not available, PDF generation will not work');
        if (generatePDFButton) {
            generatePDFButton.title = 'html2pdf knižnica nie je dostupná';
            generatePDFButton.style.opacity = '0.5';
        }
    } else {
        console.log('html2pdf is available and ready');
        if (generatePDFButton) {
            generatePDFButton.title = 'Generovať PDF ponuku';
            generatePDFButton.style.opacity = '1';
        }
    }
}

function initializeTabSystem() {
    // Set default active tab
    const defaultTab = 'quotes';
    switchTab(defaultTab);

    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.classList.remove('show');
            event.target.style.display = 'none';
        }
    });
}

// Event Listeners
function initializeEventListeners() {
    // Service selection
    if (serviceCheckboxes) {
        serviceCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', handleServiceChange);
        });
    }

    // Custom input fields
    document.addEventListener('input', handleCustomInputChange);

    // Customer form
    if (customerForm) {
        const customerInputs = customerForm.querySelectorAll('input');
        customerInputs.forEach(input => {
            input.addEventListener('input', updateCustomerData);
        });
    }

    // Generate PDF button
    if (generatePDFButton) {
        generatePDFButton.addEventListener('click', generatePDF);
    }

    // Navigation
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavigation);
    });
}

// Navigation
function initializeNavigation() {
    // Categories are handled by CSS classes now
}

function handleNavigation(e) {
    e.preventDefault();
    const targetId = e.currentTarget.getAttribute('href').substring(1);

    // Update active nav link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    e.currentTarget.classList.add('active');

    // Show target category
    document.querySelectorAll('.service-category').forEach(category => {
        category.classList.remove('active');
    });

    const targetCategory = document.getElementById(targetId);
    if (targetCategory) {
        targetCategory.classList.add('active', 'fade-in');
    }
}

// Service Selection
function handleServiceChange(e) {
    const checkbox = e.target;
    const serviceName = checkbox.getAttribute('data-service');
    const basePrice = parseFloat(checkbox.getAttribute('data-price')) || 0;
    const isCustom = checkbox.hasAttribute('data-custom');
    
    if (checkbox.checked) {
        let finalPrice = basePrice;
        
        // Handle custom pricing
        if (isCustom) {
            finalPrice = calculateCustomPrice(checkbox);
        }
        
        const service = {
            name: serviceName,
            price: finalPrice,
            basePrice: basePrice,
            isCustom: isCustom,
            element: checkbox
        };
        
        selectedServices.push(service);
        
        // Show custom input if needed
        showCustomInput(checkbox);
    } else {
        // Remove service from selection
        selectedServices = selectedServices.filter(service => service.name !== serviceName);
        
        // Hide custom input
        hideCustomInput(checkbox);
    }
    
    updateCalculation();
    updateSelectedServicesList();
}

function calculateCustomPrice(checkbox) {
    const serviceItem = checkbox.closest('.service-item');
    const customInput = serviceItem.querySelector('.custom-input input');
    const multiplier = parseFloat(customInput.getAttribute('data-multiplier'));
    const additional = parseFloat(customInput.getAttribute('data-additional'));
    const basePrice = parseFloat(checkbox.getAttribute('data-price')) || 0;
    
    if (customInput && customInput.value) {
        const inputValue = parseFloat(customInput.value) || 0;
        
        if (multiplier) {
            return inputValue * multiplier;
        } else if (additional) {
            return inputValue + additional;
        }
    }
    
    return basePrice;
}

function handleCustomInputChange(e) {
    const input = e.target;
    if (input.closest('.custom-input')) {
        const serviceItem = input.closest('.service-item');
        const checkbox = serviceItem.querySelector('input[type="checkbox"]');
        
        if (checkbox && checkbox.checked) {
            // Update the service price
            const serviceName = checkbox.getAttribute('data-service');
            const newPrice = calculateCustomPrice(checkbox);
            
            // Update in selectedServices array
            const serviceIndex = selectedServices.findIndex(service => service.name === serviceName);
            if (serviceIndex !== -1) {
                selectedServices[serviceIndex].price = newPrice;
                updateCalculation();
                updateSelectedServicesList();
            }
        }
    }
}

function showCustomInput(checkbox) {
    const serviceItem = checkbox.closest('.service-item');
    const customInput = serviceItem.querySelector('.custom-input');
    if (customInput) {
        customInput.style.display = 'block';
        customInput.classList.add('slide-in');
    }
}

function hideCustomInput(checkbox) {
    const serviceItem = checkbox.closest('.service-item');
    const customInput = serviceItem.querySelector('.custom-input');
    if (customInput) {
        customInput.style.display = 'none';
        customInput.classList.remove('slide-in');
        // Reset input value
        const input = customInput.querySelector('input');
        if (input) {
            input.value = '';
        }
    }
}

// Calculation
function updateCalculation() {
    const subtotal = selectedServices.reduce((sum, service) => sum + service.price, 0);
    const vat = subtotal * 0.20; // 20% DPH
    const total = subtotal + vat;
    
    // Update display
    if (subtotalElement) subtotalElement.textContent = formatPrice(subtotal);
    if (vatElement) vatElement.textContent = formatPrice(vat);
    if (totalElement) totalElement.textContent = formatPrice(total);

    // Enable/disable PDF button
    if (generatePDFButton) {
        const hasServices = selectedServices.length > 0;
        const hasCustomerData = validateCustomerData();
        generatePDFButton.disabled = !hasServices || !hasCustomerData;
    }
}

function updateSelectedServicesList() {
    if (!selectedServicesList) return;

    if (selectedServices.length === 0) {
        selectedServicesList.innerHTML = '<p class="no-services">Žiadne služby nie sú vybrané</p>';
        return;
    }
    
    const servicesHTML = selectedServices.map(service => `
        <div class="selected-service">
            <span class="service-name-calc">${service.name}</span>
            <span class="service-price-calc">${formatPrice(service.price)}</span>
        </div>
    `).join('');
    
    selectedServicesList.innerHTML = servicesHTML;
}

function formatPrice(price) {
    return new Intl.NumberFormat('sk-SK', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2
    }).format(price);
}

// Customer Data
function updateCustomerData() {
    customerData = {
        name: document.getElementById('customerName').value,
        phone: document.getElementById('customerPhone').value,
        email: document.getElementById('customerEmail').value,
        address: document.getElementById('customerAddress').value,
        cemetery: document.getElementById('cemetery').value
    };
    
    updateCalculation();
}

function validateCustomerData() {
    return customerData.name && 
           customerData.phone && 
           customerData.email && 
           isValidEmail(customerData.email);
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// PDF Generation with html2pdf for perfect Unicode support
async function generatePDF() {
    if (selectedServices.length === 0) {
        alert('Prosím vyberte aspoň jednu službu.');
        return;
    }

    if (!validateCustomerData()) {
        alert('Prosím vyplňte všetky povinné údaje o zákazníkovi.');
        return;
    }

    // Show loading state
    if (generatePDFButton) {
        generatePDFButton.classList.add('loading');
        generatePDFButton.disabled = true;
        generatePDFButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generujem PDF...';
    }

    try {
        // Check if html2pdf is available
        if (typeof window.html2pdf === 'undefined') {
            throw new Error('html2pdf knižnica nie je dostupná');
        }

        // Load the PDF template
        const templateResponse = await fetch('pdf-template.html');
        if (!templateResponse.ok) {
            throw new Error('Nepodarilo sa načítať PDF template');
        }

        const templateHTML = await templateResponse.text();

        // Create a temporary container
        const tempContainer = document.createElement('div');
        tempContainer.innerHTML = templateHTML;
        tempContainer.style.position = 'absolute';
        tempContainer.style.left = '-9999px';
        tempContainer.style.top = '-9999px';
        document.body.appendChild(tempContainer);

        // Fill in customer data
        const customerNameEl = tempContainer.querySelector('#customer-name');
        const customerPhoneEl = tempContainer.querySelector('#customer-phone');
        const customerEmailEl = tempContainer.querySelector('#customer-email');
        const customerAddressEl = tempContainer.querySelector('#customer-address');

        if (customerNameEl) customerNameEl.textContent = customerData.name || '-';
        if (customerPhoneEl) customerPhoneEl.textContent = customerData.phone || '-';
        if (customerEmailEl) customerEmailEl.textContent = customerData.email || '-';

        // Combine address and cemetery
        let fullAddress = '';
        if (customerData.address) fullAddress += customerData.address;
        if (customerData.cemetery) {
            if (fullAddress) fullAddress += ', ';
            fullAddress += `Cintorín: ${customerData.cemetery}`;
        }
        if (customerAddressEl) customerAddressEl.textContent = fullAddress || '-';
        // Fill in services
        const servicesListEl = tempContainer.querySelector('#services-list');
        if (servicesListEl) {
            servicesListEl.innerHTML = '';
            selectedServices.forEach((service, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="service-name">${service.name}</td>
                    <td style="text-align: center;">1</td>
                    <td class="service-price">${formatPrice(service.price)}</td>
                `;
                servicesListEl.appendChild(row);
            });
        }
        // Calculate and fill in totals
        const subtotal = selectedServices.reduce((sum, service) => sum + service.price, 0);
        const vat = subtotal * 0.20;
        const total = subtotal + vat;

        const subtotalEl = tempContainer.querySelector('#subtotal');
        const vatEl = tempContainer.querySelector('#vat');
        const totalEl = tempContainer.querySelector('#total');
        const issueDateEl = tempContainer.querySelector('#issue-date');

        if (subtotalEl) subtotalEl.textContent = formatPrice(subtotal);
        if (vatEl) vatEl.textContent = formatPrice(vat);
        if (totalEl) totalEl.textContent = formatPrice(total);
        if (issueDateEl) issueDateEl.textContent = new Date().toLocaleDateString('sk-SK');

        // Get the PDF content element
        const pdfContent = tempContainer.querySelector('#pdf-content');
        if (!pdfContent) {
            throw new Error('PDF template element not found');
        }

        // Configure html2pdf options
        const options = {
            margin: [10, 10, 10, 10],
            filename: `cenova-ponuka-${customerData.name.replace(/\s+/g, '-').toLowerCase()}-${new Date().toISOString().split('T')[0]}.pdf`,
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                letterRendering: true
            },
            jsPDF: {
                unit: 'mm',
                format: 'a4',
                orientation: 'portrait',
                compress: true
            }
        };

        // Generate and save PDF
        await html2pdf().set(options).from(pdfContent).save();

        // Clean up
        document.body.removeChild(tempContainer);

    } catch (error) {
        console.error('Error generating PDF:', error);
        let errorMessage = 'Nastala chyba pri generovaní PDF.';

        if (error.message.includes('html2pdf')) {
            errorMessage = 'html2pdf knižnica nie je dostupná. Skúste obnoviť aplikáciu.';
        } else if (error.message.includes('template')) {
            errorMessage = 'Nepodarilo sa načítať PDF template.';
        } else if (error.message.includes('save')) {
            errorMessage = 'Chyba pri ukladaní PDF súboru.';
        }

        alert(errorMessage + '\n\nDetail chyby: ' + error.message);

        // Clean up temp container if it exists
        const tempContainer = document.querySelector('div[style*="-9999px"]');
        if (tempContainer) {
            document.body.removeChild(tempContainer);
        }
    } finally {
        // Reset button state
        if (generatePDFButton) {
            generatePDFButton.classList.remove('loading');
            generatePDFButton.disabled = false;
            generatePDFButton.innerHTML = '<i class="fas fa-file-pdf"></i> Generovať PDF ponuku';
        }
        updateCalculation(); // This will re-enable the button if conditions are met
    }
}

// Save/Load Functions
async function saveQuote() {
    const quoteData = {
        customerData: customerData,
        selectedServices: selectedServices.map(service => ({
            name: service.name,
            price: service.price,
            basePrice: service.basePrice,
            isCustom: service.isCustom
        })),
        timestamp: new Date().toISOString()
    };

    const dataStr = JSON.stringify(quoteData, null, 2);
    const filename = `ponuka-${customerData.name || 'nova'}-${new Date().toISOString().split('T')[0]}.json`;

    if (isElectron && window.electronAPI) {
        // Use Electron's native save dialog
        try {
            const result = await window.electronAPI.saveFile(dataStr, filename);
            if (result.success) {
                alert('Ponuka bola úspešne uložená.');
            } else if (!result.canceled) {
                alert('Chyba pri ukladaní: ' + result.error);
            }
        } catch (error) {
            console.error('Error saving file:', error);
            alert('Chyba pri ukladaní súboru.');
        }
    } else {
        // Fallback to browser download
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();

        URL.revokeObjectURL(url);
    }
}

async function loadQuote() {
    if (isElectron && window.electronAPI) {
        // Use Electron's native open dialog
        try {
            const result = await window.electronAPI.openFile();
            if (result.success) {
                loadQuoteData(JSON.parse(result.data));
            } else if (!result.canceled) {
                alert('Chyba pri načítavaní: ' + result.error);
            }
        } catch (error) {
            console.error('Error loading file:', error);
            alert('Chyba pri načítavaní súboru. Prosím skontrolujte formát súboru.');
        }
    } else {
        // Fallback to browser file input
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const quoteData = JSON.parse(e.target.result);
                        loadQuoteData(quoteData);
                    } catch (error) {
                        alert('Chyba pri načítavaní súboru. Prosím skontrolujte formát súboru.');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }
}

function loadQuoteData(quoteData) {
    // Load customer data
    customerData = quoteData.customerData;
    document.getElementById('customerName').value = customerData.name || '';
    document.getElementById('customerPhone').value = customerData.phone || '';
    document.getElementById('customerEmail').value = customerData.email || '';
    document.getElementById('customerAddress').value = customerData.address || '';
    document.getElementById('cemetery').value = customerData.cemetery || '';

    // Clear current selections
    serviceCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
        hideCustomInput(checkbox);
    });
    selectedServices = [];

    // Load selected services
    quoteData.selectedServices.forEach(savedService => {
        const checkbox = document.querySelector(`input[data-service="${savedService.name}"]`);
        if (checkbox) {
            checkbox.checked = true;
            selectedServices.push({
                name: savedService.name,
                price: savedService.price,
                basePrice: savedService.basePrice,
                isCustom: savedService.isCustom,
                element: checkbox
            });

            if (savedService.isCustom) {
                showCustomInput(checkbox);
            }
        }
    });

    updateCalculation();
    updateSelectedServicesList();

    alert('Ponuka bola úspešne načítaná.');
}
